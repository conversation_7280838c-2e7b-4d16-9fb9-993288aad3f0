!function(e){"use strict";function t(e=0){var t=document.getElementsByClassName("setup-content"),n=document.getElementById("better-payment-prev"),a=document.getElementById("better-payment-next"),s=document.getElementById("better-payment-save");t.length<1||(t[e].style.display="block",n.style.display=0==e?"none":"inline",e==t.length-1?(s.style.display="inline",a.style.display="none"):(a.style.display="inline",s.style.display="none"),function(e){var t=document.getElementsByClassName("step");document.getElementsByClassName("better-payment-setup-wizard")[0].setAttribute("data-step",e);for(var n=0;n<t.length;n++)t[n].className=t[n].className.replace(" active","");t[e].className+=" active"}(e))}e(document).on("change",".better_payment_preferences",(function(t){var n=e(this).val(),a=e(".better-payment-elements-container .better-payment-elements-info input[type=checkbox]");a.length>0&&("custom"==n?a.prop("checked",!0):(a.prop("checked",!1),a.each((function(t,a){("advance"==n&&""!=e(a).data("preferences")||e(a).data("preferences")==n)&&e(a).prop("checked",!0)}))))})),t(),e(document).on("click","#better-payment-next,#better-payment-prev",(function(e){var n=document.getElementsByClassName("better-payment-setup-wizard"),a=parseInt(n[0].getAttribute("data-step")),s=document.getElementsByClassName("setup-content");if(s[a].style.display="none",(a="better-payment-prev"==e.target.id?a-1:a+1)>=s.length)return!1;t(a)})),e(".btn-collect").on("click",(function(){e(".better-payment-whatwecollecttext").toggle()})),e(document).on("change",'.quick-setup-stripe input[name="better_payment_settings_payment_stripe_live_mode"]',(function(t){t.preventDefault();let n=e(this).attr("data-targettest");e(this).is(":checked")&&(n=e(this).attr("data-targetlive")),e(".bp-stripe-key").removeClass("bp-d-block").addClass("bp-d-none"),e(`.${n}`).removeClass("bp-d-none").addClass("bp-d-block")})),e(document).on("click",".better-payment-setup-wizard-save",(function(t){t.preventDefault(),e(this).parents("#better-payment-admin-settings-form"),function(t){let n=e(t),a=betterPaymentObjWizard.nonce,s=e("#better-payment-admin-settings-form").serializeArray();console.log({bpAdminSettingsSaveBtn:n,nonce:a,formDataWizard:s,ajaxurl}),e.ajax({type:"POST",url:ajaxurl,data:{action:"save_setup_wizard_data",nonce:a,form_data:s},beforeSend:function(){n.addClass("is-loading")},success:function(e){n.removeClass("is-loading"),e.success?Swal.fire({timer:3e3,showConfirmButton:!1,imageUrl:betterPaymentObjWizard.success_image}).then((t=>{window.location=e.data.redirect_url})):Swal.fire({type:"error",title:"Error",text:"error"})}})}(this)}))}(jQuery);